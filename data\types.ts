export interface Exercise {
  id: string;
  title?: string;
  question?: string;
  solution?: string;
  hint?: string;
  exerciseImageUrl?: string;
  solutionImageUrl?: string;
  displayOrder?: number;
}

export interface Exam {
  id: string;
  lessonId: string;
  title?: string;
  hint?: string;
  exerciseImageUrl?: string;
  solutionImageUrl?: string;
  displayOrder?: number;
}

export interface Homework {
  id: string;
  lessonId: string;
  title?: string;
  question?: string;
  solution?: string;
  hint?: string;
  exerciseImageUrl?: string;
  solutionImageUrl?: string;
  displayOrder?: number;
}

export interface Summary {
  id: string;
  lessonId: string;
  title?: string;
  hint?: string;
  exerciseImageUrl?: string;
  displayOrder?: number;
}

export interface Lesson {
  id: string;
  title: string;
  description: string;
  subjectId: string;
  exercises: Exercise[];
  content_type?: 'exercise' | 'homework' | 'summary' | 'exam';
  display_order?: number;
}

export interface Subject {
  id: string;
  name: string;
  icon: string;
  yearId: string;
  description?: string;
}

export interface Year {
  id: string;
  name: string;
  levelId: string;
  description: string;
}

export interface Level {
  id: string;
  name: string;
  description: string;
}
